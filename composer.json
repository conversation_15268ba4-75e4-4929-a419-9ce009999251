{"name": "nextcloud/whiteboard", "config": {"autoloader-suffix": "Whiteboard", "optimize-autoloader": true, "platform": {"php": "8.4"}, "sort-packages": true}, "license": "AGPL", "require": {"php": "^8.0", "firebase/php-jwt": "^6.10"}, "require-dev": {"nextcloud/coding-standard": "^1.3.2", "nextcloud/ocp": "dev-master", "phpunit/phpunit": "^12", "psalm/phar": "^6.0", "psr/log": "^3.0.2", "roave/security-advisories": "dev-latest", "sabre/dav": "^4.3"}, "scripts": {"lint": "find . -name \\*.php -not -path './vendor/*' -print0 | xargs -0 -n1 php -l", "cs:check": "PHP_CS_FIXER_IGNORE_ENV=1 php-cs-fixer fix --dry-run", "cs:fix": "PHP_CS_FIXER_IGNORE_ENV=1 php-cs-fixer fix", "psalm": "psalm.phar", "test:unit": "phpunit -c tests/phpunit.xml"}}