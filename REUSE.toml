# SPDX-FileCopyrightText: 2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: AGPL-3.0-or-later
version = 1
SPDX-PackageName = "whiteboard"
SPDX-PackageSupplier = "Nextcloud <<EMAIL>>"
SPDX-PackageDownloadLocation = "https://github.com/nextcloud/whiteboard"

[[annotations]]
path = [".gitattributes", ".editorconfig", "babel.config.js", ".php-cs-fixer.dist.php", "package-lock.json", "package.json", "composer.json", "composer.lock", "webpack.js", "stylelint.config.js", ".eslintrc.js", "cypress/.eslintrc.json", ".gitignore", ".jshintrc", ".l10nignore", "action/.gitignore", "action/package.json", "action/package-lock.json", "action/dist/index.js", "tests/**", "psalm.xml", "vendor-bin/**/composer.json", "vendor-bin/**/composer.lock", ".tx/config", "webpack.config.js", "js/vendor.LICENSE.txt", ".github/CODEOWNERS", "vite.config.js", "stylelint.config.cjs", "composer/**.php", "composer/composer.**", "tsconfig.json", "jsconfig.json", "krankerl.toml", "renovate.json", ".github/ISSUE_TEMPLATE/**", ".nextcloudignore", "CHANGELOG.md", ".tsconfig.json"]
precedence = "aggregate"
SPDX-FileCopyrightText = "none"
SPDX-License-Identifier = "CC0-1.0"

[[annotations]]
path = ["l10n/**.js", "l10n/**.json", "js/**.mjs.map", "js/**.mjs", "css/**", "screenshots/**"]
precedence = "aggregate"
SPDX-FileCopyrightText = "2019-2024 Nextcloud GmbH and Nextcloud contributors"
SPDX-License-Identifier = "AGPL-3.0-or-later"

[[annotations]]
path = ["img/app.svg", "img/app-dark.svg", "img/app-filetype.svg"]
precedence = "aggregate"
SPDX-FileCopyrightText = "2019 Simran-B <https://pictogrammers.com/contributor/Simran-B/>"
SPDX-License-Identifier = "Apache-2.0"
