# This workflow is provided via the organization template repository
#
# https://github.com/nextcloud/.github
# https://docs.github.com/en/actions/learn-github-actions/sharing-workflows-with-your-organization
#
# SPDX-FileCopyrightText: 2023-2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: MIT

name: Node tests

on:
  pull_request:
  push:
    branches:
      - main
      - master
      - stable*

permissions:
  contents: read

concurrency:
  group: node-tests-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  changes:
    runs-on: ubuntu-latest-low
    permissions:
      contents: read
      pull-requests: read

    outputs:
      src: ${{ steps.changes.outputs.src}}

    steps:
      - uses: dorny/paths-filter@de90cc6fb38fc0963ad72b210f1f284cd68cea36 # v3.0.2
        id: changes
        continue-on-error: true
        with:
          filters: |
            src:
              - '.github/workflows/**'
              - '__tests__/**'
              - '__mocks__/**'
              - 'src/**'
              - 'appinfo/info.xml'
              - 'package.json'
              - 'package-lock.json'
              - 'tsconfig.json'
              - '**.js'
              - '**.ts'
              - '**.vue'

  test:
    runs-on: ubuntu-latest

    needs: changes
    if: needs.changes.outputs.src != 'false'

    steps:
      - name: Checkout
        uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7

      - name: Read package.json node and npm engines version
        uses: skjnldsv/read-package-engines-version-actions@06d6baf7d8f41934ab630e97d9e6c0bc9c9ac5e4 # v3
        id: versions
        with:
          fallbackNode: '^20'
          fallbackNpm: '^10'

      - name: Set up node ${{ steps.versions.outputs.nodeVersion }}
        uses: actions/setup-node@1e60f620b9541d16bece96c5465dc8ee9832be0b # v4.0.3
        with:
          node-version: ${{ steps.versions.outputs.nodeVersion }}

      - name: Set up npm ${{ steps.versions.outputs.npmVersion }}
        run: npm i -g 'npm@${{ steps.versions.outputs.npmVersion }}'

      - name: Install dependencies & build
        env:
          CYPRESS_INSTALL_BINARY: 0
        run: |
          npm ci
          npm run build --if-present

      - name: Test
        run: npm run test --if-present

      - name: Test and process coverage
        run: npm run test:coverage --if-present

      - name: Collect coverage
        uses: codecov/codecov-action@e28ff129e5465c2c0dcc6f003fc735cb6ae0c673 # v4.5.0
        with:
          files: ./coverage/lcov.info

  summary:
    permissions:
      contents: none
    runs-on: ubuntu-latest-low
    needs: [changes, test]

    if: always()

    name: test-summary

    steps:
      - name: Summary status
        run: if ${{ needs.changes.outputs.src != 'false' && needs.test.result != 'success' }}; then exit 1; fi
