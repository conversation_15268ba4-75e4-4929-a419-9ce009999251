# This workflow is provided via the organization template repository
#
# https://github.com/nextcloud/.github
# https://docs.github.com/en/actions/learn-github-actions/sharing-workflows-with-your-organization
#
# SPDX-FileCopyrightText: 2022-2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: MIT

name: PHPUnit SQLite

on: pull_request

permissions:
  contents: read

concurrency:
  group: phpunit-sqlite-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  matrix:
    runs-on: ubuntu-latest-low
    outputs:
      php-version: ${{ steps.versions.outputs.php-available-list }}
      server-max: ${{ steps.versions.outputs.branches-max-list }}
    steps:
      - name: Checkout app
        uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7

      - name: Get version matrix
        id: versions
        uses: icewind1991/nextcloud-version-matrix@58becf3b4bb6dc6cef677b15e2fd8e7d48c0908f # v1.3.1

  changes:
    runs-on: ubuntu-latest-low
    permissions:
      contents: read
      pull-requests: read

    outputs:
      src: ${{ steps.changes.outputs.src}}

    steps:
      - uses: dorny/paths-filter@de90cc6fb38fc0963ad72b210f1f284cd68cea36 # v3.0.2
        id: changes
        continue-on-error: true
        with:
          filters: |
            src:
              - '.github/workflows/**'
              - 'appinfo/**'
              - 'lib/**'
              - 'templates/**'
              - 'tests/**'
              - 'vendor/**'
              - 'vendor-bin/**'
              - '.php-cs-fixer.dist.php'
              - 'composer.json'
              - 'composer.lock'

  phpunit-sqlite:
    runs-on: ubuntu-latest

    needs: [changes, matrix]
    if: needs.changes.outputs.src != 'false'

    strategy:
      matrix:
        php-versions: ${{ fromJson(needs.matrix.outputs.php-version) }}
        server-versions: ${{ fromJson(needs.matrix.outputs.server-max) }}

    name: SQLite PHP ${{ matrix.php-versions }} Nextcloud ${{ matrix.server-versions }}

    steps:
      - name: Set app env
        run: |
          # Split and keep last
          echo "APP_NAME=${GITHUB_REPOSITORY##*/}" >> $GITHUB_ENV

      - name: Checkout server
        uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7
        with:
          submodules: true
          repository: nextcloud/server
          ref: ${{ matrix.server-versions }}

      - name: Checkout app
        uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7
        with:
          path: apps/${{ env.APP_NAME }}

      - name: Set up php ${{ matrix.php-versions }}
        uses: shivammathur/setup-php@c541c155eee45413f5b09a52248675b1a2575231 # v2.31.1
        with:
          php-version: ${{ matrix.php-versions }}
          # https://docs.nextcloud.com/server/stable/admin_manual/installation/source_installation.html#prerequisites-for-manual-installation
          extensions: bz2, ctype, curl, dom, fileinfo, gd, iconv, intl, json, libxml, mbstring, openssl, pcntl, posix, session, simplexml, xmlreader, xmlwriter, zip, zlib, sqlite, pdo_sqlite
          coverage: none
          ini-file: development
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Check composer file existence
        id: check_composer
        uses: andstor/file-existence-action@076e0072799f4942c8bc574a82233e1e4d13e9d6 # v3.0.0
        with:
          files: apps/${{ env.APP_NAME }}/composer.json

      - name: Set up dependencies
        # Only run if phpunit config file exists
        if: steps.check_composer.outputs.files_exists == 'true'
        working-directory: apps/${{ env.APP_NAME }}
        run: composer i

      - name: Set up Nextcloud
        env:
          DB_PORT: 4444
        run: |
          mkdir data
          ./occ maintenance:install --verbose --database=sqlite --database-name=nextcloud --database-host=127.0.0.1 --database-port=$DB_PORT --database-user=root --database-pass=rootpassword --admin-user admin --admin-pass admin
          ./occ app:enable --force ${{ env.APP_NAME }}

      - name: Check PHPUnit script is defined
        id: check_phpunit
        continue-on-error: true
        working-directory: apps/${{ env.APP_NAME }}
        run: |
          composer run --list | grep '^  test:unit ' | wc -l | grep 1

      - name: PHPUnit
        # Only run if phpunit config file exists
        if: steps.check_phpunit.outcome == 'success'
        working-directory: apps/${{ env.APP_NAME }}
        run: composer run test:unit

      - name: Check PHPUnit integration script is defined
        id: check_integration
        continue-on-error: true
        working-directory: apps/${{ env.APP_NAME }}
        run: |
          composer run --list | grep '^  test:integration ' | wc -l | grep 1

      - name: Run Nextcloud
        # Only run if phpunit integration config file exists
        if: steps.check_integration.outcome == 'success'
        run: php -S localhost:8080 &

      - name: PHPUnit integration
        # Only run if phpunit integration config file exists
        if: steps.check_integration.outcome == 'success'
        working-directory: apps/${{ env.APP_NAME }}
        run: composer run test:integration

      - name: Print logs
        if: always()
        run: |
          cat data/nextcloud.log

      - name: Skipped
        # Fail the action when neither unit nor integration tests ran
        if: steps.check_phpunit.outcome == 'failure' && steps.check_integration.outcome == 'failure'
        run: |
          echo 'Neither PHPUnit nor PHPUnit integration tests are specified in composer.json scripts'
          exit 1

  summary:
    permissions:
      contents: none
    runs-on: ubuntu-latest-low
    needs: [changes, phpunit-sqlite]

    if: always()

    name: phpunit-sqlite-summary

    steps:
      - name: Summary status
        run: if ${{ needs.changes.outputs.src != 'false' && needs.phpunit-sqlite.result != 'success' }}; then exit 1; fi
