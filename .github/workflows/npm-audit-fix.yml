# This workflow is provided via the organization template repository
#
# https://github.com/nextcloud/.github
# https://docs.github.com/en/actions/learn-github-actions/sharing-workflows-with-your-organization
#
# SPDX-FileCopyrightText: 2023-2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: MIT

name: Npm audit fix and compile

on:
  workflow_dispatch:
  schedule:
    # At 2:30 on Sundays
    - cron: '30 2 * * 0'

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        branches: ['main', 'master', 'stable29', 'stable28', 'stable27']

    name: npm-audit-fix-${{ matrix.branches }}

    steps:
      - name: Checkout
        uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7
        with:
          ref: ${{ matrix.branches }}

      - name: Read package.json node and npm engines version
        uses: skjnldsv/read-package-engines-version-actions@06d6baf7d8f41934ab630e97d9e6c0bc9c9ac5e4 # v3
        id: versions
        with:
          fallbackNode: '^20'
          fallbackNpm: '^10'

      - name: Set up node ${{ steps.versions.outputs.nodeVersion }}
        uses: actions/setup-node@1e60f620b9541d16bece96c5465dc8ee9832be0b # v4.0.3
        with:
          node-version: ${{ steps.versions.outputs.nodeVersion }}

      - name: Set up npm ${{ steps.versions.outputs.npmVersion }}
        run: npm i -g 'npm@${{ steps.versions.outputs.npmVersion }}'

      - name: Fix npm audit
        id: npm-audit
        uses: nextcloud-libraries/npm-audit-action@2a60bd2e79cc77f2cc4d9a3fe40f1a69896f3a87 # v0.1.0

      - name: Run npm ci and npm run build
        if: always()
        env:
          CYPRESS_INSTALL_BINARY: 0
        run: |
          npm ci
          npm run build --if-present

      - name: Create Pull Request
        if: always()
        uses: peter-evans/create-pull-request@c5a7806660adbe173f04e3e038b0ccdcd758773c # v6.1.0
        with:
          token: ${{ secrets.COMMAND_BOT_PAT }}
          commit-message: 'fix(deps): Fix npm audit'
          committer: GitHub <<EMAIL>>
          author: nextcloud-command <<EMAIL>>
          signoff: true
          branch: automated/noid/${{ matrix.branches }}-fix-npm-audit
          title: '[${{ matrix.branches }}] Fix npm audit'
          body: ${{ steps.npm-audit.outputs.markdown }}
          labels: |
            dependencies
            3. to review
