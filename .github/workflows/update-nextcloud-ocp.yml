# This workflow is provided via the organization template repository
#
# https://github.com/nextcloud/.github
# https://docs.github.com/en/actions/learn-github-actions/sharing-workflows-with-your-organization
#
# SPDX-FileCopyrightText: 2022-2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: MIT

name: Update nextcloud/ocp

on:
  workflow_dispatch:
  schedule:
    - cron: "5 2 * * 0"

jobs:
  update-nextcloud-ocp:
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        branches: ['main', 'master', 'stable29', 'stable28', 'stable27']

    name: update-nextcloud-ocp-${{ matrix.branches }}

    steps:
      - id: checkout
        uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7
        with:
          ref: ${{ matrix.branches }}
          submodules: true
        continue-on-error: true

      - name: Set up php8.2
        if: steps.checkout.outcome == 'success'
        uses: shivammathur/setup-php@c541c155eee45413f5b09a52248675b1a2575231 # v2.31.1
        with:
          php-version: 8.2
          # https://docs.nextcloud.com/server/stable/admin_manual/installation/source_installation.html#prerequisites-for-manual-installation
          extensions: bz2, ctype, curl, dom, fileinfo, gd, iconv, intl, json, libxml, mbstring, openssl, pcntl, posix, session, simplexml, xmlreader, xmlwriter, zip, zlib, sqlite, pdo_sqlite
          coverage: none
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Read codeowners
        if: steps.checkout.outcome == 'success'
        id: codeowners
        run: |
          grep '/appinfo/info.xml' .github/CODEOWNERS | cut -f 2- -d ' ' | xargs | awk '{ print "codeowners="$0 }' >> $GITHUB_OUTPUT
        continue-on-error: true

      - name: Composer install
        if: steps.checkout.outcome == 'success'
        run: composer install

      - name: Composer update nextcloud/ocp
        id: update_branch
        if: ${{ steps.checkout.outcome == 'success' && matrix.branches != 'main' }}
        run: composer require --dev 'nextcloud/ocp:dev-${{ matrix.branches }}'

      - name: Raise on issue on failure
        uses: dacbd/create-issue-action@cdb57ab6ff8862aa09fee2be6ba77a59581921c2 # v2.0.0
        if: ${{ steps.checkout.outcome == 'success' && failure() && steps.update_branch.conclusion == 'failure' }}
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          title: 'Failed to update nextcloud/ocp package on branch ${{ matrix.branches }}'
          body: 'Please check the output of the GitHub action and manually resolve the issues<br>${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}<br>${{ steps.codeowners.outputs.codeowners }}'

      - name: Composer update nextcloud/ocp
        id: update_main
        if: ${{ steps.checkout.outcome == 'success' && matrix.branches == 'main' }}
        run: composer require --dev nextcloud/ocp:dev-master

      - name: Raise on issue on failure
        uses: dacbd/create-issue-action@cdb57ab6ff8862aa09fee2be6ba77a59581921c2 # v2.0.0
        if: ${{ steps.checkout.outcome == 'success' && failure() && steps.update_main.conclusion == 'failure' }}
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          title: 'Failed to update nextcloud/ocp package on branch ${{ matrix.branches }}'
          body: 'Please check the output of the GitHub action and manually resolve the issues<br>${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}<br>${{ steps.codeowners.outputs.codeowners }}'

      - name: Reset checkout 3rdparty
        if: steps.checkout.outcome == 'success'
        run: |
          git clean -f 3rdparty
          git checkout 3rdparty
        continue-on-error: true

      - name: Reset checkout vendor
        if: steps.checkout.outcome == 'success'
        run: |
          git clean -f vendor
          git checkout vendor
        continue-on-error: true

      - name: Reset checkout vendor-bin
        if: steps.checkout.outcome == 'success'
        run: |
          git clean -f vendor-bin
          git checkout vendor-bin
        continue-on-error: true

      - name: Create Pull Request
        if: steps.checkout.outcome == 'success'
        uses: peter-evans/create-pull-request@c5a7806660adbe173f04e3e038b0ccdcd758773c # v6.1.0
        with:
          token: ${{ secrets.COMMAND_BOT_PAT }}
          commit-message: 'chore(dev-deps): Bump nextcloud/ocp package'
          committer: GitHub <<EMAIL>>
          author: nextcloud-command <<EMAIL>>
          signoff: true
          branch: 'automated/noid/${{ matrix.branches }}-update-nextcloud-ocp'
          title: '[${{ matrix.branches }}] Update nextcloud/ocp dependency'
          body: |
            Auto-generated update of [nextcloud/ocp](https://github.com/nextcloud-deps/ocp/) dependency
          labels: |
            dependencies
            3. to review
