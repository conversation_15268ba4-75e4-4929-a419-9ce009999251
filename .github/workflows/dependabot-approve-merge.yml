# This workflow is provided via the organization template repository
#
# https://github.com/nextcloud/.github
# https://docs.github.com/en/actions/learn-github-actions/sharing-workflows-with-your-organization
#
# SPDX-FileCopyrightText: 2021-2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: MIT

name: Dependabot

on:
  pull_request_target:
    branches:
      - main
      - master
      - stable*

permissions:
  contents: read

concurrency:
  group: dependabot-approve-merge-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  auto-approve-merge:
    if: github.actor == 'dependabot[bot]' || github.actor == 'renovate[bot]'
    runs-on: ubuntu-latest-low
    permissions:
      # for hmarr/auto-approve-action to approve PRs
      pull-requests: write

    steps:
      - name: Disabled on forks
        if: ${{ github.event.pull_request.head.repo.full_name != github.repository }}
        run: |
          echo 'Can not approve PRs from forks'
          exit 1

      # GitHub actions bot approve
      - uses: hmarr/auto-approve-action@b40d6c9ed2fa10c9a2749eca7eb004418a705501 # v2
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}

      # Nextcloud bot approve and merge request
      - uses: ahmadnassri/action-dependabot-auto-merge@45fc124d949b19b6b8bf6645b6c9d55f4f9ac61a # v2
        with:
          target: minor
          github-token: ${{ secrets.DEPENDABOT_AUTOMERGE_TOKEN }}
