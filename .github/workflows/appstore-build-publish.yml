# This workflow is provided via the organization template repository
#
# https://github.com/nextcloud/.github
# https://docs.github.com/en/actions/learn-github-actions/sharing-workflows-with-your-organization
#
# SPDX-FileCopyrightText: 2021-2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: MIT

name: Build and publish app release

on:
  release:
    types: [published]

jobs:
  build_and_publish:
    runs-on: ubuntu-latest

    # Only allowed to be run on nextcloud-releases repositories
    if: ${{ github.repository_owner == 'nextcloud-releases' }}

    steps:
      - name: Check actor permission
        uses: skjnldsv/check-actor-permission@69e92a3c4711150929bca9fcf34448c5bf5526e7 # v3.0
        with:
          require: write

      - name: Set app env
        run: |
          # Split and keep last
          echo "APP_NAME=${GITHUB_REPOSITORY##*/}" >> $GITHUB_ENV
          echo "APP_VERSION=${GITHUB_REF##*/}" >> $GITHUB_ENV

      - name: Checkout
        uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7
        with:
          path: ${{ env.APP_NAME }}

      - name: Get appinfo data
        id: appinfo
        uses: skjnldsv/xpath-action@7e6a7c379d0e9abc8acaef43df403ab4fc4f770c # master
        with:
          filename: ${{ env.APP_NAME }}/appinfo/info.xml
          expression: "//info//dependencies//nextcloud/@min-version"

      - name: Read package.json node and npm engines version
        uses: skjnldsv/read-package-engines-version-actions@06d6baf7d8f41934ab630e97d9e6c0bc9c9ac5e4 # v3
        id: versions
        # Continue if no package.json
        continue-on-error: true
        with:
          path: ${{ env.APP_NAME }}
          fallbackNode: '^20'
          fallbackNpm: '^10'

      - name: Set up node ${{ steps.versions.outputs.nodeVersion }}
        # Skip if no package.json
        if: ${{ steps.versions.outputs.nodeVersion }}
        uses: actions/setup-node@1e60f620b9541d16bece96c5465dc8ee9832be0b # v4.0.3
        with:
          node-version: ${{ steps.versions.outputs.nodeVersion }}

      - name: Set up npm ${{ steps.versions.outputs.npmVersion }}
        # Skip if no package.json
        if: ${{ steps.versions.outputs.npmVersion }}
        run: npm i -g 'npm@${{ steps.versions.outputs.npmVersion }}'

      - name: Get php version
        id: php-versions
        uses: icewind1991/nextcloud-version-matrix@58becf3b4bb6dc6cef677b15e2fd8e7d48c0908f # v1.3.1
        with:
          filename: ${{ env.APP_NAME }}/appinfo/info.xml

      - name: Set up php ${{ steps.php-versions.outputs.php-min }}
        uses: shivammathur/setup-php@c541c155eee45413f5b09a52248675b1a2575231 # v2.31.1
        with:
          php-version: ${{ steps.php-versions.outputs.php-min }}
          coverage: none
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Check composer.json
        id: check_composer
        uses: andstor/file-existence-action@076e0072799f4942c8bc574a82233e1e4d13e9d6 # v3.0.0
        with:
          files: "${{ env.APP_NAME }}/composer.json"

      - name: Install composer dependencies
        if: steps.check_composer.outputs.files_exists == 'true'
        run: |
          cd ${{ env.APP_NAME }}
          composer install --no-dev

      - name: Build ${{ env.APP_NAME }}
        # Skip if no package.json
        if: ${{ steps.versions.outputs.nodeVersion }}
        env:
          CYPRESS_INSTALL_BINARY: 0
        run: |
          cd ${{ env.APP_NAME }}
          npm ci
          npm run build --if-present

      - name: Check Krankerl config
        id: krankerl
        uses: andstor/file-existence-action@076e0072799f4942c8bc574a82233e1e4d13e9d6 # v3.0.0
        with:
          files: ${{ env.APP_NAME }}/krankerl.toml

      - name: Install Krankerl
        if: steps.krankerl.outputs.files_exists == 'true'
        run: |
          wget https://github.com/ChristophWurst/krankerl/releases/download/v0.14.0/krankerl_0.14.0_amd64.deb
          sudo dpkg -i krankerl_0.14.0_amd64.deb

      - name: Package ${{ env.APP_NAME }} ${{ env.APP_VERSION }} with krankerl
        if: steps.krankerl.outputs.files_exists == 'true'
        run: |
          cd ${{ env.APP_NAME }}
          krankerl package

      - name: Package ${{ env.APP_NAME }} ${{ env.APP_VERSION }} with makefile
        if: steps.krankerl.outputs.files_exists != 'true'
        run: |
          cd ${{ env.APP_NAME }}
          make appstore

      - name: Checkout server ${{ fromJSON(steps.appinfo.outputs.result).nextcloud.min-version }}
        continue-on-error: true
        id: server-checkout
        run: |
          NCVERSION='${{ fromJSON(steps.appinfo.outputs.result).nextcloud.min-version }}'
          wget --quiet https://download.nextcloud.com/server/releases/latest-$NCVERSION.zip
          unzip latest-$NCVERSION.zip

      - name: Checkout server master fallback
        uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7
        if: ${{ steps.server-checkout.outcome != 'success' }}
        with:
          submodules: true
          repository: nextcloud/server
          path: nextcloud

      - name: Sign app
        run: |
          # Extracting release
          cd ${{ env.APP_NAME }}/build/artifacts
          tar -xvf ${{ env.APP_NAME }}.tar.gz
          cd ../../../
          # Setting up keys
          echo '${{ secrets.APP_PRIVATE_KEY }}' > ${{ env.APP_NAME }}.key
          wget --quiet "https://github.com/nextcloud/app-certificate-requests/raw/master/${{ env.APP_NAME }}/${{ env.APP_NAME }}.crt"
          # Signing
          php nextcloud/occ integrity:sign-app --privateKey=../${{ env.APP_NAME }}.key --certificate=../${{ env.APP_NAME }}.crt --path=../${{ env.APP_NAME }}/build/artifacts/${{ env.APP_NAME }}
          # Rebuilding archive
          cd ${{ env.APP_NAME }}/build/artifacts
          tar -zcvf ${{ env.APP_NAME }}.tar.gz ${{ env.APP_NAME }}

      - name: Attach tarball to github release
        uses: svenstaro/upload-release-action@04733e069f2d7f7f0b4aebc4fbdbce8613b03ccd # v2
        id: attach_to_release
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          file: ${{ env.APP_NAME }}/build/artifacts/${{ env.APP_NAME }}.tar.gz
          asset_name: ${{ env.APP_NAME }}-${{ env.APP_VERSION }}.tar.gz
          tag: ${{ github.ref }}
          overwrite: true

      - name: Upload app to Nextcloud appstore
        uses: nextcloud-releases/nextcloud-appstore-push-action@a011fe619bcf6e77ddebc96f9908e1af4071b9c1 # v1
        with:
          app_name: ${{ env.APP_NAME }}
          appstore_token: ${{ secrets.APPSTORE_TOKEN }}
          download_url: ${{ steps.attach_to_release.outputs.browser_download_url }}
          app_private_key: ${{ secrets.APP_PRIVATE_KEY }}
