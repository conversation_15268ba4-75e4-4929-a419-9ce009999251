# This workflow is provided via the organization template repository
#
# https://github.com/nextcloud/.github
# https://docs.github.com/en/actions/learn-github-actions/sharing-workflows-with-your-organization
#
# SPDX-FileCopyrightText: 2022-2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: MIT

name: Static analysis

on: pull_request

concurrency:
  group: psalm-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  static-analysis:
    runs-on: ubuntu-latest

    name: static-psalm-analysis
    steps:
      - name: Checkout
        uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7

      - name: Get php version
        id: versions
        uses: icewind1991/nextcloud-version-matrix@58becf3b4bb6dc6cef677b15e2fd8e7d48c0908f # v1.3.1

      - name: Set up php${{ steps.versions.outputs.php-available }}
        uses: shivammathur/setup-php@c541c155eee45413f5b09a52248675b1a2575231 # v2.31.1
        with:
          php-version: ${{ steps.versions.outputs.php-available }}
          extensions: bz2, ctype, curl, dom, fileinfo, gd, iconv, intl, json, libxml, mbstring, openssl, pcntl, posix, session, simplexml, xmlreader, xmlwriter, zip, zlib, sqlite, pdo_sqlite
          coverage: none
          ini-file: development
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Install dependencies
        run: composer i

      - name: Run coding standards check
        run: composer run psalm
