# SPDX-FileCopyrightText: 2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: MIT
name: Playwright Tests
on:
  pull_request:
    branches: [main]

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    steps:
      - name: Checkout app
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Check composer.json
        id: check_composer
        uses: andstor/file-existence-action@076e0072799f4942c8bc574a82233e1e4d13e9d6 # v2
        with:
          files: 'composer.json'

      - name: Install composer dependencies
        if: steps.check_composer.outputs.files_exists == 'true'
        run: composer install --no-dev

      - name: Read package.json node and npm engines version
        uses: skjnldsv/read-package-engines-version-actions@06d6baf7d8f41934ab630e97d9e6c0bc9c9ac5e4 # v3
        id: versions
        with:
          fallbackNode: '^20'
          fallbackNpm: '^10'

      - name: Set up node ${{ steps.versions.outputs.nodeVersion }}
        uses: actions/setup-node@1d0ff469b7ec7b3cb9d8673fde0c81c44821de2a # v4.2.0
        with:
          node-version: ${{ steps.versions.outputs.nodeVersion }}

      - name: Set up npm ${{ steps.versions.outputs.npmVersion }}
        run: npm i -g npm@"${{ steps.versions.outputs.npmVersion }}"

      - name: Install node dependencies & build app
        run: |
          npm ci
          TESTING=true npm run build --if-present

      - name: Install Playwright Browsers
        run: npx playwright install chromium --with-deps

      - name: Run Playwright tests
        run: |
          npx playwright test

      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30
