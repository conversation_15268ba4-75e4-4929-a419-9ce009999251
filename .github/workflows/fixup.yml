# This workflow is provided via the organization template repository
#
# https://github.com/nextcloud/.github
# https://docs.github.com/en/actions/learn-github-actions/sharing-workflows-with-your-organization
#
# SPDX-FileCopyrightText: 2021-2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: MIT

name: Block fixup and squash commits

on:
  pull_request:
    types: [opened, ready_for_review, reopened, synchronize]

permissions:
  contents: read

concurrency:
  group: fixup-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  commit-message-check:
    if: github.event.pull_request.draft == false

    permissions:
      pull-requests: write
    name: Block fixup and squash commits

    runs-on: ubuntu-latest-low

    steps:
      - name: Run check
        uses: skjnldsv/block-fixup-merge-action@c138ea99e45e186567b64cf065ce90f7158c236a # v2
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
