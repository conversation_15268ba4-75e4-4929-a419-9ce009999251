<?xml version="1.0" encoding="utf-8"?>
<!--
  - SPDX-FileCopyrightText: 2024 Nextcloud GmbH and Nextcloud contributors
  - SPDX-License-Identifier: AGPL-3.0-or-later
-->
<info xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://apps.nextcloud.com/schema/apps/info.xsd">
	<id>whiteboard</id>
	<name>Whiteboard</name>
	<summary>Whiteboard app</summary>
	<description><![CDATA[

The official whiteboard app for Nextcloud. It allows users to create and share whiteboards with other users and collaborate in real-time.

**Whiteboard requires a separate collaboration server to work.** Please see the [documentation](https://github.com/nextcloud/whiteboard?tab=readme-ov-file#backend) on how to install it.

- 🎨 Drawing shapes, writing text, connecting elements
- 📝 Real-time collaboration
- 🖼️ Add images with drag and drop
- 📊 Easily add mermaid diagrams
- ✨ Use the Smart Picker to embed other elements from Nextcloud
- 📦 Image export
- 💪 Strong foundation: We use Excalidraw as our base library

]]>
	</description>
	<version>1.1.0-beta.1</version>
	<licence>agpl</licence>
	<author><PERSON>rtl</author>
	<namespace>Whiteboard</namespace>
	<documentation>
		<admin>https://github.com/nextcloud/whiteboard/blob/main/README.md</admin>
	</documentation>
	<category>tools</category>
	<category>files</category>
	<category>office</category>

	<website>https://github.com/nextcloud/whiteboard</website>
	<bugs>https://github.com/nextcloud/whiteboard/issues</bugs>
	<repository>https://github.com/nextcloud/whiteboard.git</repository>
	<screenshot>https://raw.githubusercontent.com/nextcloud/whiteboard/main/screenshots/screenshot1.png</screenshot>

	<dependencies>
		<nextcloud min-version="28" max-version="32"/>
	</dependencies>

	<settings>
		<admin>OCA\Whiteboard\Settings\Admin</admin>
		<admin-section>OCA\Whiteboard\Settings\Section</admin-section>
	</settings>
</info>
