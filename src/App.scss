/**
 * SPDX-FileCopyrightText: 2020 Excalidraw
 * SPDX-License-Identifier: MIT
 */

// https://github.com/excalidraw/excalidraw/blob/4dc4590f247a0a0d9c3f5d39fe09c00c5cef87bf/examples/excalidraw
.excalidraw,
.App .excalidraw {
	--zIndex-modal: 100010;
	--zIndex-popup: 100020;
	--ui-font: var(--font-face);

	--color-brand: var(--color-primary-element) !important;
	--color-brand-hover: var(--color-primary-element-hover) !important;
	--color-primary: var(--color-primary-element) !important;
	--color-primary-darker: var(--color-primary-element-hover) !important;
	--color-primary-darkest: var(----color-primary-element-hover) !important;
	--color-primary-light: var(--color-primary-element-light) !important;
	--color-surface-primary-container: var(--color-primary-element-light);
	--color-surface-low: var(--color-primary-element-light);
	--color-surface-high: var(--color-primary-element-light-hover) !important;
	--border-radius-lg: var(--border-radius-large);
	--border-radius-md: var(--border-radius);

	.Stack button {
		margin: 0;
		min-width: 34px;
	}

	.color-picker__top-picks {
		gap: var(--default-grid-baseline);
	}
	.App-menu__left {
		width: 270px;
	}
}

:root {
	--zIndex-popup: 100020 !important;
}

.widgets--list {
	height: 100%;
}

.widgets--list > div {
	height: 100% !important;
}

.widgets--list > div > div {
	height: 100% !important;
}

.widget-custom {
	height: 100%;
	margin: 0 !important;
}

.widget-file--interactive {
	height: 100% !important;
	max-height: 100% !important;
	min-height: unset !important;
}

.text-menubar--ready {
	backdrop-filter: unset !important;
	-webkit-backdrop-filter: unset !important;
}

.App {
	position: relative;
	height: 100%;
	width: 100%;

	.App-container {
		width: 100%;
		height: 100%;
		position: relative;
	}

	.App-loading,
	.App-error {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 100%;
		width: 100%;
		color: var(--color-main-text);

		.retry-button {
			margin-top: 16px;
			padding: 8px 16px;
			background-color: var(--color-primary);
			color: var(--color-primary-text);
			border: none;
			border-radius: 4px;
			cursor: pointer;

			&:hover {
				background-color: var(--color-primary-element-hover);
			}
		}
	}

	.App-error {
		color: var(--color-error);
	}
}

.excalidraw-wrapper {
	height: 100%;
	position: relative;
	overflow: hidden;
}

:root[dir='ltr']
	.excalidraw
	.layer-ui__wrapper
	.zen-mode-transition.App-menu_bottom--transition-left {
	transform: none;
}

.excalidraw .panelColumn {
	text-align: left;
}

.export-wrapper {
	display: flex;
	flex-direction: column;
	margin: 50px;

	&__checkbox {
		display: flex;
	}
}

.layer-ui__wrapper__top-right label[title='Library'] {
	display: none;
}

.ImageExportModal__preview__filename {
	display: none;
}

.excalidraw .Switch input,
.excalidraw .RadioGroup__choice input {
	opacity: 0;
}

// Custom Context Menu
.excalidraw .context-menu-item__label {
	font-weight: normal !important;
}

.excalidraw .context-menu {
	position: relative !important;
	border-radius: var(--border-radius-large) !important;
	box-shadow: none !important;
	filter: drop-shadow(
		0 calc(var(--default-grid-baseline) / 4)
			calc(var(--default-grid-baseline) * 2.5) var(--color-box-shadow)
	) !important;
	list-style: none !important;
	-webkit-user-select: none !important;
	user-select: none !important;
	margin: calc(var(--default-grid-baseline) * -1) 0 0
		calc(var(--default-grid-baseline) / 2) !important;
	padding: calc(var(--default-grid-baseline))
		calc(var(--default-grid-baseline)) !important;
	background-color: var(--color-main-background) !important;
	cursor: default !important;
	border: none !important;

	kbd:empty {
		display: none !important;
	}
}

.excalidraw .context-menu-item-separator {
	border-top: calc(var(--default-grid-baseline) / 8) solid var(--color-border) !important;
	margin-top: calc(var(--default-grid-baseline)) !important;
	margin-bottom: calc(var(--default-grid-baseline)) !important;
}

.excalidraw .context-menu-item {
	color: var(--popup-text-color) !important;
	margin-top: 0 !important;
	margin-bottom: 0 !important;
	padding-left: calc(var(--default-grid-baseline) * 3) !important;
}

.excalidraw .context-menu-item:hover {
	background-color: var(--color-background-hover) !important;
	border-radius: var(--border-radius-large) !important;
}

.network-status {
	display: flex;
	align-items: center;
	padding: 8px;
	border-radius: 8px;
	transition: all 0.2s ease;
	position: absolute;
	bottom: 80px; /* Position at the bottom with enough space for other UI elements */
	right: 12px;
	z-index: 10000; /* Increased z-index to ensure it's above all other UI elements */
	max-width: 40px;
	cursor: pointer;
	opacity: 0.9;
	font-size: 13px;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);

	&__icon-container {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		min-width: 24px;
		min-height: 24px;
	}

	&__loading-spinner {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	&__content {
		display: flex;
		align-items: center;
		overflow: hidden;
		max-width: 0;
		opacity: 0;
		transition: all 0.2s ease;
	}

	&__text {
		white-space: nowrap;
		margin-left: 8px;
		font-weight: 600;
		font-size: 13px;
	}

	&--offline {
		background-color: rgba(244, 67, 54, 0.2);
		color: rgba(244, 67, 54, 1);
		border: 1px solid rgba(244, 67, 54, 0.4);
		/* Use will-change to isolate animation and prevent image flickering */
		will-change: box-shadow, border-color;
		transform: translateZ(0);
		animation: pulse-red 2s infinite;
	}

	&--connecting {
		background-color: rgba(255, 152, 0, 0.2);
		color: rgba(255, 152, 0, 1);
		border: 1px solid rgba(255, 152, 0, 0.4);
		/* Use will-change to isolate animation and prevent image flickering */
		will-change: box-shadow, border-color;
		transform: translateZ(0);
		animation: pulse-orange 2s infinite;
	}

	&--reconnecting {
		background-color: rgba(255, 152, 0, 0.2);
		color: rgba(255, 152, 0, 1);
		border: 1px solid rgba(255, 152, 0, 0.4);
		/* Use will-change to isolate animation and prevent image flickering */
		will-change: box-shadow, border-color;
		transform: translateZ(0);
		animation: pulse-orange 2s infinite;
	}

	&--online {
		background-color: rgba(76, 175, 80, 0.2);
		color: rgba(76, 175, 80, 1);
		border: 1px solid rgba(76, 175, 80, 0.4);
	}

	&--expanded {
		max-width: 150px;
		padding: 8px 12px;
		z-index: 10001; /* Ensure expanded state is above other UI elements */
		background-color: var(--color-main-background);

		.network-status__content {
			max-width: 110px;
			opacity: 1;
		}
	}

	&:hover {
		opacity: 1;
		box-shadow: 0 3px 8px rgba(0, 0, 0, 0.25);
		background-color: var(--color-main-background);
		max-width: 150px;

		.network-status__content {
			max-width: 110px;
			opacity: 1;
		}
	}

	&:focus-visible {
		outline: 2px solid var(--color-primary);
		outline-offset: 2px;
	}
}

/* Performance optimized animations that only animate opacity to reduce repaints */
@keyframes pulse-red {
	0% {
		box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.4);
		border-color: rgba(244, 67, 54, 0.4);
		opacity: 0.9;
	}
	50% {
		box-shadow: 0 0 0 5px rgba(244, 67, 54, 0.2);
		border-color: rgba(244, 67, 54, 0.6);
		opacity: 1;
	}
	100% {
		box-shadow: 0 0 0 0 rgba(244, 67, 54, 0);
		border-color: rgba(244, 67, 54, 0.4);
		opacity: 0.9;
	}
}

@keyframes pulse-orange {
	0% {
		box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.4);
		border-color: rgba(255, 152, 0, 0.4);
		opacity: 0.9;
	}
	50% {
		box-shadow: 0 0 0 5px rgba(255, 152, 0, 0.2);
		border-color: rgba(255, 152, 0, 0.6);
		opacity: 1;
	}
	100% {
		box-shadow: 0 0 0 0 rgba(255, 152, 0, 0);
		border-color: rgba(255, 152, 0, 0.4);
		opacity: 0.9;
	}
}

@media (prefers-reduced-motion) {
	.network-status {
		transition: none;
		animation: none !important;

		&__content {
			transition: none;
		}
	}
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
	.network-status {
		bottom: 90px;
		right: 8px;
		padding: 6px;
		max-width: 36px;
		z-index: 10001; /* Ensure it's above all UI elements on mobile */

		&--expanded {
			max-width: 130px;
			padding: 6px 10px;
		}

		&:hover {
			max-width: 130px;

			.network-status__content {
				max-width: 90px;
			}
		}
	}
}

.App-loading {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
	width: 100%;
	font-size: 1.2rem;
	color: var(--color-main-text);
}
