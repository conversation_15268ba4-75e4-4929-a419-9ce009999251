OC.L10N.register(
    "whiteboard",
    {
    "New whiteboard" : "Nová tabuľa",
    "Create new whiteboard" : "Vytvoriť novú tabuľu",
    "Whiteboard" : "Tabuľa",
    "Whiteboard server URL is not configured. Whiteboard requires a separate collaboration server that is connected to Nextcloud." : "URL Whiteboardu/Tabule nie je nakonfigurovaná. Whiteboard/Tabuľa vyžaduje samostatný server spolupráce, ktorý je pripojený k Nextcloud.",
    "Nextcloud server could not connect to whiteboard server: %s" : "Server Nextcloud sa nemôže pripojiť k serveru Whiteboard: %s",
    "No version provided by /status enpdoint" : "/status enpdoint neposkytuje informáciu o verzii",
    "Backend server is running a different version, make sure to upgrade both to the same version. App: %s Backend version: %s" : "Backendový server používa inú verziu, uistite sa, že aktualizujete obe na rovnakú verziu. Aplikácia: %s Backend verzia: %s",
    "Whiteboard backend server could not reach Nextcloud: %s" : "Backend server Whiteboard sa nemôže pripojiť k Nextcloudu: %s",
    "Failed to connect to whiteboard server status endpoint: %s" : "Nepodarilo sa pripojiť ku koncovému bodu so stavom servera Whiteboard: %s",
    "Whiteboard server configured properly" : "Server Whiteboard je správne nastavený",
    "Whiteboard app" : "Aplikácia Tabuľa",
    "The official whiteboard app for Nextcloud. It allows users to create and share whiteboards with other users and collaborate in real-time.\n\n**Whiteboard requires a separate collaboration server to work.** Please see the [documentation](https://github.com/nextcloud/whiteboard?tab=readme-ov-file#backend) on how to install it.\n\n- 🎨 Drawing shapes, writing text, connecting elements\n- 📝 Real-time collaboration\n- 🖼️ Add images with drag and drop\n- 📊 Easily add mermaid diagrams\n- ✨ Use the Smart Picker to embed other elements from Nextcloud\n- 📦 Image export\n- 💪 Strong foundation: We use Excalidraw as our base library" : "Oficiálna aplikácia Whiteboard/Tabuľa pre Nextcloud. Umožňuje používateľom vytvárať a zdieľať tabule s inými používateľmi a spolupracovať v reálnom čase.\n\n**Whiteboard/Tabuľa vyžaduje na fungovanie samostatný server pre spoluprácu.** Pozrite si [dokumentáciu](https://github.com/nextcloud/whiteboard?tab=readme-ov-file#backend), ako ju nainštalovať.\n\n- 🎨 Kreslenie tvarov, písanie textu, spájanie prvkov\n- 📝 Spolupráca v reálnom čase\n- 🖼️ Pridajte obrázky pomocou drag and drop\n- 📊 Jednoducho pridajte stĺpcové grafy\n- ✨ Použite Inteligentý Výber na vloženie ďalších prvkov z Nextcloud\n- 📦 Export obrázkov\n- 💪 Silný základ: Excalidraw používame ako našu základnú knižnicu",
    "Whiteboard server" : "Server Whiteboard",
    "Whiteboard backend server is configured and connected." : "Backend server tabule je nakonfigurovaný a pripojený.",
    "Failed to verify the connection:" : "Nepodarilo sa overiť spojenie:",
    "Verifying connection…" : "Overujem spojenie...",
    "Whiteboard requires a separate collaboration server that is connected to Nextcloud." : "Whiteboard/Tabuľa vyžaduje samostatný server spolupráce, ktorý je pripojený k Nextcloud.",
    "See the documentation on how to install it." : "Pozrite si dokumentáciu ako ho nainštalovať",
    "Whiteboard server URL" : "URL servera Whiteboard",
    "This URL is used by the browser to connect to the whiteboard server." : "Táto URL sa používa prehliadačom pre pripojenie k serveru Whiteboard.",
    "Internal whiteboard server URL" : "Interná URL servera Whiteboard",
    "This URL is used by the Nextcloud server to connect to the whiteboard server." : "Táto URL sa používa Serverom Nextcloud pre pripojenie k serveru Whiteboard",
    "Skip TLS certificate validation (not recommended)" : "Preskočiť validáciu certifikátu TLS (neodporúča sa)",
    "Shared secret" : "Shared secret",
    "Save settings" : "Uložiť nastavenia",
    "Advanced settings" : "Rozšírené nastavenia",
    "Max file size" : "Maximálna veľkosť súboru"
},
"nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);");
