OC.L10N.register(
    "whiteboard",
    {
    "New whiteboard" : "Novo quadro",
    "Create new whiteboard" : "Criar novo quadro",
    "Whiteboard" : "Quadro branco",
    "Whiteboard server URL is not configured. Whiteboard requires a separate collaboration server that is connected to Nextcloud." : "O URL do servidor de quadro branco não está configurado. O quadro branco requer um servidor de colaboração separado que esteja conectado ao Nextcloud.",
    "Nextcloud server could not connect to whiteboard server: %s" : "O servidor Nextcloud não pôde se conectar ao servidor de quadro branco: %s",
    "No version provided by /status enpdoint" : "Nenhuma versão fornecida pelo recurso /status",
    "Backend server is running a different version, make sure to upgrade both to the same version. App: %s Backend version: %s" : "O servidor de back-end está executando uma versão diferente; assegure-se de atualizar ambos para a mesma versão. Aplicativo: %s Versão de backend: %s",
    "Whiteboard backend server could not reach Nextcloud: %s" : "O servidor de back-end do quadro branco não pôde acessar o Nextcloud: %s",
    "Failed to connect to whiteboard server status endpoint: %s" : "Falha ao conectar-se ao recurso de status do servidor de quadro branco: %s",
    "Whiteboard server configured properly" : "Servidor de quadro branco configurado corretamente",
    "Whiteboard app" : "Quadro Branco App",
    "The official whiteboard app for Nextcloud. It allows users to create and share whiteboards with other users and collaborate in real-time.\n\n**Whiteboard requires a separate collaboration server to work.** Please see the [documentation](https://github.com/nextcloud/whiteboard?tab=readme-ov-file#backend) on how to install it.\n\n- 🎨 Drawing shapes, writing text, connecting elements\n- 📝 Real-time collaboration\n- 🖼️ Add images with drag and drop\n- 📊 Easily add mermaid diagrams\n- ✨ Use the Smart Picker to embed other elements from Nextcloud\n- 📦 Image export\n- 💪 Strong foundation: We use Excalidraw as our base library" : "O aplicativo oficial do quadro branco para o NextCloud. Ele permite que os usuários criem e compartilhem quadros brancos com outros usuários e colaborem em tempo real.\n\n** Whiteboard exige que um servidor de colaboração separado funcione.\n\n- 🎨 Desenho de formas, escrevendo texto, elementos de conexão\n- 📝 Colaboração em tempo real\n- 🖼️ Adicione imagens com arrasto e gota\n- 📊 Adicione facilmente diagramas de sereia\n- ✨ Use o seletor inteligente para incorporar outros elementos do NextCloud\n- 📦 Exportação de imagem\n- 💪 Fundação forte: usamos a Excalidraw como nossa biblioteca base",
    "Whiteboard server" : "Servidor de quadro branco",
    "Whiteboard backend server is configured and connected." : "O servidor de backend para o whiteboard está configurado e conectado.",
    "Failed to verify the connection:" : "Falha ao verificar a conexão:",
    "Verifying connection…" : "Verificando conexão...",
    "Whiteboard requires a separate collaboration server that is connected to Nextcloud." : "Quadro Branco requer um servidor de colaboração separado conectado ao Nextcloud.",
    "See the documentation on how to install it." : "Veja a documentação de como instalá-lo.",
    "Whiteboard server URL" : "URL do servidor Whiteboard",
    "This URL is used by the browser to connect to the whiteboard server." : "Esse URL é usado pelo navegador para se conectar ao servidor do quadro branco.",
    "Internal whiteboard server URL" : "URL interna do servidor de quadro branco",
    "This URL is used by the Nextcloud server to connect to the whiteboard server." : "Essa URL é usada pelo servidor Nextcloud para se conectar ao servidor do quadro branco.",
    "Skip TLS certificate validation (not recommended)" : "Ignorar a validação do certificado TLS (não recomendado)",
    "Shared secret" : "Segredo compartilhado",
    "Save settings" : "Salvar configurações",
    "Advanced settings" : "Configurações avançadas",
    "Max file size" : "Tamanho máximo de arquivo"
},
"nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;");
