{"translations": {"New whiteboard": "New whiteboard", "Create new whiteboard": "Create new whiteboard", "Whiteboard": "Whiteboard", "Whiteboard server URL is not configured. Whiteboard requires a separate collaboration server that is connected to Nextcloud.": "Whiteboard server URL is not configured. Whiteboard requires a separate collaboration server that is connected to Nextcloud.", "Nextcloud server could not connect to whiteboard server: %s": "Nextcloud server could not connect to whiteboard server: %s", "No version provided by /status enpdoint": "No version provided by /status enpdoint", "Backend server is running a different version, make sure to upgrade both to the same version. App: %s Backend version: %s": "Backend server is running a different version, make sure to upgrade both to the same version. App: %s Backend version: %s", "Whiteboard backend server could not reach Nextcloud: %s": "Whiteboard backend server could not reach Nextcloud: %s", "Failed to connect to whiteboard server status endpoint: %s": "Failed to connect to whiteboard server status endpoint: %s", "Whiteboard server configured properly": "Whiteboard server configured properly", "Whiteboard app": "Whiteboard app", "The official whiteboard app for Nextcloud. It allows users to create and share whiteboards with other users and collaborate in real-time.\n\n**Whiteboard requires a separate collaboration server to work.** Please see the [documentation](https://github.com/nextcloud/whiteboard?tab=readme-ov-file#backend) on how to install it.\n\n- 🎨 Drawing shapes, writing text, connecting elements\n- 📝 Real-time collaboration\n- 🖼️ Add images with drag and drop\n- 📊 Easily add mermaid diagrams\n- ✨ Use the Smart Picker to embed other elements from Nextcloud\n- 📦 Image export\n- 💪 Strong foundation: We use Excalidraw as our base library": "The official whiteboard app for Nextcloud. It allows users to create and share whiteboards with other users and collaborate in real-time.\n\n**Whiteboard requires a separate collaboration server to work.** Please see the [documentation](https://github.com/nextcloud/whiteboard?tab=readme-ov-file#backend) on how to install it.\n\n- 🎨 Drawing shapes, writing text, connecting elements\n- 📝 Real-time collaboration\n- 🖼️ Add images with drag and drop\n- 📊 Easily add mermaid diagrams\n- ✨ Use the Smart Picker to embed other elements from Nextcloud\n- 📦 Image export\n- 💪 Strong foundation: We use Excalidraw as our base library", "Whiteboard server": "Whiteboard server", "Whiteboard backend server is configured and connected.": "Whiteboard backend server is configured and connected.", "Failed to verify the connection:": "Failed to verify the connection:", "Verifying connection…": "Verifying connection…", "Whiteboard requires a separate collaboration server that is connected to Nextcloud.": "Whiteboard requires a separate collaboration server that is connected to Nextcloud.", "See the documentation on how to install it.": "See the documentation on how to install it.", "Whiteboard server URL": "Whiteboard server URL", "This URL is used by the browser to connect to the whiteboard server.": "This URL is used by the browser to connect to the whiteboard server.", "Internal whiteboard server URL": "Internal whiteboard server URL", "This URL is used by the Nextcloud server to connect to the whiteboard server.": "This URL is used by the Nextcloud server to connect to the whiteboard server.", "Skip TLS certificate validation (not recommended)": "Skip TLS certificate validation (not recommended)", "Shared secret": "Shared secret", "Save settings": "Save settings", "Advanced settings": "Advanced settings", "Max file size": "Max file size"}, "pluralForm": "nplurals=2; plural=(n != 1);"}