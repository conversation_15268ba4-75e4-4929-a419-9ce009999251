OC.L10N.register(
    "whiteboard",
    {
    "New whiteboard" : "Neues Whiteboard",
    "Create new whiteboard" : "Neues Whiteboard",
    "Whiteboard" : "Whiteboard",
    "Whiteboard server URL is not configured. Whiteboard requires a separate collaboration server that is connected to Nextcloud." : "Die URL des Whiteboard-Servers ist nicht konfiguriert. Whiteboard erfordert einen separaten Collaboration-Server, der mit Nextcloud verbunden ist.",
    "Nextcloud server could not connect to whiteboard server: %s" : "Der Nextcloud-Server konnte keine Verbindung zum Whiteboard-Server herstellen: %s",
    "No version provided by /status enpdoint" : "Keine Version vom /status-Endpunkt bereitgestellt",
    "Backend server is running a different version, make sure to upgrade both to the same version. App: %s Backend version: %s" : "Auf dem Backend-Server läuft eine andere Version. Sicherstellen, dass beide auf die gleiche Version aktualisiert werden. App: %s Backend-Version: %s",
    "Whiteboard backend server could not reach Nextcloud: %s" : "Der Whiteboard-Backend-Server konnte Nextcloud nicht erreichen: %s",
    "Failed to connect to whiteboard server status endpoint: %s" : "Verbindung zum Whiteboard-Server-Serverstatus-Endpunkt konnte nicht hergestellt werden: %s",
    "Whiteboard server configured properly" : "Whiteboard-Server richtig eingerichtet",
    "Whiteboard app" : "Whiteboard-App",
    "The official whiteboard app for Nextcloud. It allows users to create and share whiteboards with other users and collaborate in real-time.\n\n**Whiteboard requires a separate collaboration server to work.** Please see the [documentation](https://github.com/nextcloud/whiteboard?tab=readme-ov-file#backend) on how to install it.\n\n- 🎨 Drawing shapes, writing text, connecting elements\n- 📝 Real-time collaboration\n- 🖼️ Add images with drag and drop\n- 📊 Easily add mermaid diagrams\n- ✨ Use the Smart Picker to embed other elements from Nextcloud\n- 📦 Image export\n- 💪 Strong foundation: We use Excalidraw as our base library" : "Die offizielle Whiteboard-App für Nextcloud. Sie ermöglicht es Benutzern, Whiteboards zu erstellen und mit anderen Benutzern zu teilen und in Echtzeit zusammenzuarbeiten.\n\n**Whiteboard benötigt einen separaten Collaboration-Server, um zu funktionieren.** Bitte in der [Dokumentation] (https://github.com/nextcloud/whiteboard?tab=readme-ov-file#backend) lesen, wie die Installation erfolgen soll.\n\n- 🎨 Zeichnen von Formen, Schreiben von Text, Verbinden von Elementen\n- 📝 Zusammenarbeit in Echtzeit\n- 🖼️ Bilder durch Ziehen und Ablegen hinzufügen\n- 📊 Einfaches Hinzufügen von Mermaid-Diagrammen\n- ✨ Verwende den Smart Picker, um andere Elemente aus Nextcloud einzubetten\n- 📦 Bilder exportieren\n- 💪 Starke Grundlage: Wir verwenden Excalidraw als Basisbibliothek",
    "Whiteboard server" : "Whiteboard-Server",
    "Whiteboard backend server is configured and connected." : "Whiteboard-Backend-Server ist konfiguriert und angeschlossen.",
    "Failed to verify the connection:" : "Die Verbindung konnte nicht verifiziert werden:",
    "Verifying connection…" : "Überprüfe die Verbindung…",
    "Whiteboard requires a separate collaboration server that is connected to Nextcloud." : "Whiteboard erfordert einen separaten Collaboration-Server, der mit Nextcloud verbunden ist.",
    "See the documentation on how to install it." : "Lies die Dokumentation, wie die Installation erfolgt.",
    "Whiteboard server URL" : "Whiteboard-Server-URL",
    "This URL is used by the browser to connect to the whiteboard server." : "Diese URL wird vom Browser verwendet, um eine Verbindung zum Whiteboard-Server herzustellen.",
    "Internal whiteboard server URL" : "Interne Whiteboard-Server-URL",
    "This URL is used by the Nextcloud server to connect to the whiteboard server." : "Diese URL wird vom Nextcloud-Server verwendet, um eine Verbindung mit dem Whiteboard-Server herzustellen.",
    "Skip TLS certificate validation (not recommended)" : "TLS-Zertifikatsüberprüfung überspringen (nicht empfohlen)",
    "Shared secret" : "Geteiltes Geheimnis",
    "Save settings" : "Einstellungen speichern",
    "Advanced settings" : "Erweiterte Einstellungen",
    "Max file size" : "Maximale Dateigröße"
},
"nplurals=2; plural=(n != 1);");
