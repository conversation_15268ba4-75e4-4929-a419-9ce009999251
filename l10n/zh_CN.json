{"translations": {"New whiteboard": "新建白板", "Create new whiteboard": "创建新白板", "Whiteboard": "白板", "Whiteboard app": "白板应用", "The official whiteboard app for Nextcloud. It allows users to create and share whiteboards with other users and collaborate in real-time.\n\n**Whiteboard requires a separate collaboration server to work.** Please see the [documentation](https://github.com/nextcloud/whiteboard?tab=readme-ov-file#backend) on how to install it.\n\n- 🎨 Drawing shapes, writing text, connecting elements\n- 📝 Real-time collaboration\n- 🖼️ Add images with drag and drop\n- 📊 Easily add mermaid diagrams\n- ✨ Use the Smart Picker to embed other elements from Nextcloud\n- 📦 Image export\n- 💪 Strong foundation: We use Excalidraw as our base library": "Nextcloud 的官方白板应用程序。它允许用户创建白板并与其他用户共享，并实时协作。\n\n**白板需要单独的协作服务器才能工作。** 有关如何安装它请参阅 [文档](https://github.com/nextcloud/whiteboard?tab=readme-ov-file#backend)\n\n- 🎨 绘制形状、书写文字、连接元素\n- 📝 实时协作\n- 🖼️ 通过拖放添加图像\n- 📊 轻松添加 Mermaid 流程图\n- ✨ 使用智能选择器嵌入 Nextcloud 中的其他元素\n- 📦 图像导出\n- 💪 坚实的基础：我们使用 Excalidraw 作为我们的基础库", "Whiteboard backend server is configured and connected.": "白板后端服务器已配置并连接。", "Failed to verify the connection:": "验证连接已失败：", "Verifying connection…": "正在验证连接……", "Whiteboard requires a separate collaboration server that is connected to Nextcloud.": "白板需要一个连接到 Nextcloud 的单独协作服务器。", "See the documentation on how to install it.": "请参阅有关如何安装它的文档。", "Whiteboard server URL": "白板服务器 URL", "Shared secret": "已分享密码", "Save settings": "保存设置", "Advanced settings": "高级选项"}, "pluralForm": "nplurals=1; plural=0;"}