{"translations": {"New whiteboard": "سبورة بيضاء جديدة", "Create new whiteboard": "إنشاء سبورة بيضاء جديدة", "Whiteboard": "السبورة", "Whiteboard server URL is not configured. Whiteboard requires a separate collaboration server that is connected to Nextcloud.": "لم تتم تهيئة عنوان URL لخادوم \"اللوحة البيضاء\" Whiteboard. يتطلب Whiteboard خادوماً تعاونيّاً منفصلاً متصلاً بنكست كلاود.", "Nextcloud server could not connect to whiteboard server: %s": "يتعذّر توصيل خادوم نكست كلاود بخادوم Witeboard \"وايت بورد\": %s", "No version provided by /status enpdoint": "لم يتم إعطاء الإصدار من قِبَل النقطة الحدّيّة لـ status/", "Backend server is running a different version, make sure to upgrade both to the same version. App: %s Backend version: %s": "خادوم الواجهة الخلفية يعمل على إصدار مختلف. تأكد من ترقية كليهما لنفس الإصدار. التطبيق : %s نسخة الخلفية: %s", "Whiteboard backend server could not reach Nextcloud: %s": "خلفية خادوم \"وايت بورد\" Whiteboard لم تستطع الوصول إلى نكست كلاود: %s", "Failed to connect to whiteboard server status endpoint: %s": "تعذّر التوصيل مع النقطة الحدّيّة لحالة خادوم نكست كلاود: %s", "Whiteboard server configured properly": "خادوم Whiteboard مُهيّاٌ كما يجب", "Whiteboard app": "تطبيق \"السبورة البيضاء\" Whiteboard", "The official whiteboard app for Nextcloud. It allows users to create and share whiteboards with other users and collaborate in real-time.\n\n**Whiteboard requires a separate collaboration server to work.** Please see the [documentation](https://github.com/nextcloud/whiteboard?tab=readme-ov-file#backend) on how to install it.\n\n- 🎨 Drawing shapes, writing text, connecting elements\n- 📝 Real-time collaboration\n- 🖼️ Add images with drag and drop\n- 📊 Easily add mermaid diagrams\n- ✨ Use the Smart Picker to embed other elements from Nextcloud\n- 📦 Image export\n- 💪 Strong foundation: We use Excalidraw as our base library": "تطبيق السبورة الرسمي لـ Nextcloud يسمح للمستخدمين بإنشاء ومشاركة ألواح المعلومات مع مستخدمين آخرين والتعاون في الوقت الفعلي.\n\n**يتطلب Whiteboard خادم تعاون منفصل للعمل. ** يرجى الاطلاع على [الوثائق](https://github.com/nextcloud/whiteboard?tab=readme-ov-file#backend) حول كيفية تثبيته.\n\n- 🎨 رسم الأشكال، كتابة النص، ربط العناصر\n- 📝 التعاون في الوقت الحقيقي\n- 🖼️ إضافة الصور بالسحب والإفلات\n- 📊 إضافة مخططات حورية البحر بسهولة\n- ✨ استخدم Smart Picker لتضمين عناصر أخرى من Nextcloud\n- 📦 تصدير الصور\n- 💪 أساس قوي: نستخدم Excalidraw كمكتبتنا الأساسية", "Whiteboard server": "خادوم Whiteboard", "Whiteboard backend server is configured and connected.": "خادم الواجهة الخلفية للسبورة البيضاء مُهيّأ و مُتّصل.", "Failed to verify the connection:": "فشل التحقق من الاتصال:", "Verifying connection…": "التحقق من الاتصال جارٍ ...", "Whiteboard requires a separate collaboration server that is connected to Nextcloud.": "السبورة البيضاء تحتاج إلى خادم تعاوني مستقبل متصل بنكست كلاود.", "See the documentation on how to install it.": "أنظر الوثائق حول كيفية تثبيته.", "Whiteboard server URL": "عنوان URL لخادم السبورة البيضاء", "This URL is used by the browser to connect to the whiteboard server.": "هذا العنوان URL مُستَعمَل من قِبَل المُتصَفِّح للاتصال بخادوم Whiteboard.", "Internal whiteboard server URL": "عنوان URL الداخلي لخادوم Whiteboard", "This URL is used by the Nextcloud server to connect to the whiteboard server.": "هذا العنوان مُستَعمَل من قِبَل خادوم نكست كلاود للاتصال بخادوم Whiteboard.", "Skip TLS certificate validation (not recommended)": "تجاوز التحقُّق من شهادة TLS (لايُوصَى بذلك)", "Shared secret": "كلمة السر المشتركة", "Save settings": "ح<PERSON><PERSON> الإعدادات", "Advanced settings": "إعدادات متقدمة", "Max file size": "أ<PERSON><PERSON>ى حجم للملف"}, "pluralForm": "nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;"}