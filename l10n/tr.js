OC.L10N.register(
    "whiteboard",
    {
    "New whiteboard" : "<PERSON>az tahta ekle",
    "Create new whiteboard" : "<PERSON>az tahta ekle",
    "Whiteboard" : "Beyaz tahta",
    "Whiteboard server URL is not configured. Whiteboard requires a separate collaboration server that is connected to Nextcloud." : "<PERSON>az tahta sunucu adresi yapılandırılmamış. Beyaz tahta uygulaması için, Nextcloud ile bağlantılı ayrı bir işbirliği sunucusu gereklidir.",
    "Nextcloud server could not connect to whiteboard server: %s" : "Nextcloud sunucusu beyaz tahta sunucusu ile bağlantı kuramadı: %s",
    "No version provided by /status enpdoint" : "/status uç noktası tarafından bir sürüm bildirilmedi",
    "Backend server is running a different version, make sure to upgrade both to the same version. App: %s Backend version: %s" : "Arka uç sunucusunda farklı bir sürüm çalışıyor. İkisinin de aynı sürüme yükseltildiğinden emin olun. Uygulama: %s Arka uç sürümü: %s",
    "Whiteboard backend server could not reach Nextcloud: %s" : "Beyaz tahta arka uç sunucusu Nextcloud sunucusuna erişemedi: %s",
    "Failed to connect to whiteboard server status endpoint: %s" : "Beyaz tahta sunucusunun durum uç noktası ile bağlantı kurulamadı: %s",
    "Whiteboard server configured properly" : "Beyaz tahta sunucusu doğru yapılandırılmış",
    "Whiteboard app" : "Beyaz tahta uygulaması",
    "The official whiteboard app for Nextcloud. It allows users to create and share whiteboards with other users and collaborate in real-time.\n\n**Whiteboard requires a separate collaboration server to work.** Please see the [documentation](https://github.com/nextcloud/whiteboard?tab=readme-ov-file#backend) on how to install it.\n\n- 🎨 Drawing shapes, writing text, connecting elements\n- 📝 Real-time collaboration\n- 🖼️ Add images with drag and drop\n- 📊 Easily add mermaid diagrams\n- ✨ Use the Smart Picker to embed other elements from Nextcloud\n- 📦 Image export\n- 💪 Strong foundation: We use Excalidraw as our base library" : "Nextcloud için resmi beyaz tahta uygulaması. Kullanıcıların beyaz tahtalar oluşturmasını ve diğer kullanıcılarla paylaşarak gerçek zamanlı işbirliği yapmasını sağlar.\n\n**Beyaz tahtanın çalışması için ayrı bir işbirliği sunucusu gerekir.** Lütfen nasıl kurulacağıyla ilgili [belgelere](https://github.com/nextcloud/whiteboard?tab=readme-ov-file#backend) bakın.\n\n- 🎨 Şekil çizme, metin yazma, ögeleri bağlama\n- 📝 Gerçek zamanlı işbirliği\n- 🖼️ Sürükleyip bırakarak görsel ekleme\n- 📊 Denizkızı diyagramlarını kolayca ekleme\n- ✨ Nextcloud üzerinden başka ögeler eklemek için akıllı seçiciyi kullanma\n- 📦 Görsel dışa aktarma\n- 💪 Güçlü temel: Temel kitaplığımız olarak Excalidraw kullanıyoruz",
    "Whiteboard server" : "Beyaz tahta sunucusu",
    "Whiteboard backend server is configured and connected." : "Beyaz tahta arka uç sunucusu yapılandırıldı ve bağlantı kuruldu.",
    "Failed to verify the connection:" : "Bağlantı doğrulanamadı:",
    "Verifying connection…" : "Bağlantı doğrulanıyor…",
    "Whiteboard requires a separate collaboration server that is connected to Nextcloud." : "Beyaz tahta uygulaması için, Nextcloud ile ilişki ayrı bir işbirliği sunucusu gereklidir.",
    "See the documentation on how to install it." : "Kurulumun nasıl yapılacağı ile ilgili belgelere bakın.",
    "Whiteboard server URL" : "Beyaz tahta sunucusu adresi",
    "This URL is used by the browser to connect to the whiteboard server." : "Bu adres tarayıcının beyaz tahta sunucusu ile bağlantı kurması için kullanılır.",
    "Internal whiteboard server URL" : "İç beyaz tahta sunucusu adresi",
    "This URL is used by the Nextcloud server to connect to the whiteboard server." : "Bu adres Nextcloud sunucusunun beyaz tahta sunucusu ile bağlantı kurması için kullanılır.",
    "Skip TLS certificate validation (not recommended)" : "TLS sertifikası doğrulaması atlansın (önerilmez)",
    "Shared secret" : "Paylaşılan parola",
    "Save settings" : "Ayarları kaydet",
    "Advanced settings" : "Gelişmiş ayarlar",
    "Max file size" : "En büyük dosya boyutu"
},
"nplurals=2; plural=(n > 1);");
