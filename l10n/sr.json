{"translations": {"New whiteboard": "Нова бела табла", "Create new whiteboard": "Креирај нову белу таблу", "Whiteboard": "Табла", "Whiteboard server URL is not configured. Whiteboard requires a separate collaboration server that is connected to Nextcloud.": "URL адреса сервера Беле табле није подешена. Бела табла захтева посебан сервер за колаборацију који је повезан са Nextcloud.", "Nextcloud server could not connect to whiteboard server: %s": "Nextcloud сервер није могао да се повеже на сервер беле табле: %s", "No version provided by /status enpdoint": "Крајња тачка /status није вратила ниједну верзију", "Backend server is running a different version, make sure to upgrade both to the same version. App: %s Backend version: %s": "Позадински сервер покреће другачију верзију, обезбедите да се оба ажурирају на исту верзију. Апликација: %s Верзија позадинског: %s", "Whiteboard backend server could not reach Nextcloud: %s": "Позадински сервер беле табле није могао да допре до Nextcloud сервера: %s", "Failed to connect to whiteboard server status endpoint: %s": "Није успело повезивање са status крајњом тачком сервера беле табле: %s", "Whiteboard server configured properly": "Сервер Беле табле је исправно подешен", "Whiteboard app": "Апликација Бела табла", "The official whiteboard app for Nextcloud. It allows users to create and share whiteboards with other users and collaborate in real-time.\n\n**Whiteboard requires a separate collaboration server to work.** Please see the [documentation](https://github.com/nextcloud/whiteboard?tab=readme-ov-file#backend) on how to install it.\n\n- 🎨 Drawing shapes, writing text, connecting elements\n- 📝 Real-time collaboration\n- 🖼️ Add images with drag and drop\n- 📊 Easily add mermaid diagrams\n- ✨ Use the Smart Picker to embed other elements from Nextcloud\n- 📦 Image export\n- 💪 Strong foundation: We use Excalidraw as our base library": "Званична апликација Бела табла за Nextcloud. Омогућава да корисници креирају и деле беле табле са осталим корисницима и да сарађују у реалном времену.\n\n**Да би радила, Бела табла захтева посебан сервер за сарадњу.** Молимо вас да погледате [документацију](https://github.com/nextcloud/whiteboard?tab=readme-ov-file#backend) on и сазнате како да га инсталирате.\n\n- 🎨 Цртање облика, писање текста, спајање елемената\n- 📝 Сарадња у реалном времену\n- 🖼️ Додајте слике превлачењем и упуштањем\n- 📊 Једноставно додајте mermaid дијаграме\n- ✨ Користите паметни бирач да уградите остале елементе из Nextcloud\n- 📦 Извоз слика\n- 💪 Јака основа: Excalidraw користимо као нашу базну библиотеку", "Whiteboard server": "Сервер Беле табле", "Whiteboard backend server is configured and connected.": "Сервер позадинског механизма за апликацију Бела табла је конфигурисан и успостављена је веза са њим.", "Failed to verify the connection:": "Није успела потврда везе:", "Verifying connection…": "Потврђује се веза", "Whiteboard requires a separate collaboration server that is connected to Nextcloud.": "Апликација Бела табла захтева посебан сервер за колаборацију који је повезан са Nextcloud.", "See the documentation on how to install it.": "Погледајте документацију у вези са упутством за његову инсталацију.", "Whiteboard server URL": "URL сервера Беле табле", "This URL is used by the browser to connect to the whiteboard server.": "Интернет прегледач користи ову URL адресу да се повеже на сервер беле табле.", "Internal whiteboard server URL": "Интерна URL адреса сервера беле табле", "This URL is used by the Nextcloud server to connect to the whiteboard server.": "Nextcloud сервер користи ову URL адресу да се повеже на сервер беле табле.", "Skip TLS certificate validation (not recommended)": "Прескочи проверу TLS сертификата (не препоручује се)", "Shared secret": "Дељена тајна", "Save settings": "Сачувај поставке", "Advanced settings": "Напредне поставке", "Max file size": "Макси<PERSON>ална величина фајла"}, "pluralForm": "nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);"}