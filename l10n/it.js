OC.L10N.register(
    "whiteboard",
    {
    "New whiteboard" : "Nuova lavagna",
    "Create new whiteboard" : "Crea nuova lavagna",
    "Whiteboard" : "Lavagna",
    "Whiteboard server URL is not configured. Whiteboard requires a separate collaboration server that is connected to Nextcloud." : "L'URL del server della lavagna non è configurato. Whiteboard richiede un server di collaborazione separato connesso a Nextcloud.",
    "Nextcloud server could not connect to whiteboard server: %s" : "Il server Nextcloud non è riuscito a connettersi al server della lavagna: %s",
    "No version provided by /status enpdoint" : "Nessuna versione fornita dallo stato dell'endpoint",
    "Backend server is running a different version, make sure to upgrade both to the same version. App: %s Backend version: %s" : "Il server backend esegue una versione diversa, assicurati di aggiornarli entrambi alla stessa versione.App: %s Versione backend: %s",
    "Whiteboard backend server could not reach Nextcloud: %s" : "Il server backend della lavagna non è riuscito a raggiungere Nextcloud: %s",
    "Failed to connect to whiteboard server status endpoint: %s" : "Impossibile connettersi all'endpoint di stato del server della lavagna: %s",
    "Whiteboard server configured properly" : "Server  di Whiteboard configurato correttamente",
    "Whiteboard app" : "App per lavagna",
    "The official whiteboard app for Nextcloud. It allows users to create and share whiteboards with other users and collaborate in real-time.\n\n**Whiteboard requires a separate collaboration server to work.** Please see the [documentation](https://github.com/nextcloud/whiteboard?tab=readme-ov-file#backend) on how to install it.\n\n- 🎨 Drawing shapes, writing text, connecting elements\n- 📝 Real-time collaboration\n- 🖼️ Add images with drag and drop\n- 📊 Easily add mermaid diagrams\n- ✨ Use the Smart Picker to embed other elements from Nextcloud\n- 📦 Image export\n- 💪 Strong foundation: We use Excalidraw as our base library" : "L'app ufficiale per lavagna per Nextcloud. Consente agli utenti di creare e condividere lavagne con altri utenti e collaborare in tempo reale.\n\n**La lavagna richiede un server di collaborazione separato per funzionare.** Consulta la [documentazione](https://github.com/nextcloud/whiteboard?tab=readme-ov-file#backend) per informazioni su come installarla.\n\n- 🎨 Disegna forme, scrivi testo, collega elementi\n- 📝 Collaborazione in tempo reale\n- 🖼️ Aggiungi immagini con trascinamento della selezione\n- 📊 Aggiungi facilmente diagrammi a sirena\n- ✨ Utilizza Smart Picker per incorporare altri elementi da Nextcloud\n- 📦 Esportazione delle immagini\n- 💪 Solide basi: utilizziamo Excalidraw come libreria di base",
    "Whiteboard server" : "Server di Whiteboard ",
    "Whiteboard backend server is configured and connected." : "Il server backend della lavagna è configurato e connesso.",
    "Failed to verify the connection:" : "Impossibile verificare la connessione:",
    "Verifying connection…" : "Verifica della connessione…",
    "Whiteboard requires a separate collaboration server that is connected to Nextcloud." : "Lavagna richiede un server di collaborazione separato connesso a Nextcloud.",
    "See the documentation on how to install it." : "Consultare la documentazione su come installarlo.",
    "Whiteboard server URL" : "URL del server della lavagna",
    "This URL is used by the browser to connect to the whiteboard server." : "Questo URL viene utilizzato dal browser per connettersi al server della lavagna.",
    "Internal whiteboard server URL" : "URL del server della lavagna interna",
    "This URL is used by the Nextcloud server to connect to the whiteboard server." : "Questo URL viene utilizzato dal server Nextcloud per connettersi al server della lavagna.",
    "Skip TLS certificate validation (not recommended)" : "Salta la convalida del certificato TLS (non consigliato)",
    "Shared secret" : "Segreto condiviso",
    "Save settings" : "Salva impostazioni",
    "Advanced settings" : "Impostazioni avanzate",
    "Max file size" : "Dimensione massima del file"
},
"nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;");
