# SPDX-FileCopyrightText: 2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: AGPL-3.0-or-later

# The URL of the Nextcloud instance
# This is used to read and write the file content of the whiteboard files
NEXTCLOUD_URL=http://nextcloud.local

# The port running the whiteboard backend
PORT=3002

# The secret key used to sign the JWT tokens to secure the communication
# between the Nextcloud app and the whiteboard backend
JWT_SECRET_KEY=your_secret_key

# For development purposes it can be useful to configure TLS and provide keys
# We recommend to use a reverse proxy for production
TLS=false
TLS_KEY=
TLS_CERT=

# Storage strategy for whiteboard data and socket-related temporary data
# Valid values are: 'redis' or 'lru' (Least Recently Used cache)
# This strategy is used for:
#  1. Whiteboard data storage
#  2. Socket-related temporary data (e.g., cached tokens, bound data for each socket ID)
#  3. Scaling the socket server across multiple nodes (when using 'redis')
# We strongly recommend using 'redis' for production environments
# 'lru' provides a balance of performance and memory usage for single-node setups
STORAGE_STRATEGY=lru

# Redis connection URL for data storage and socket server scaling
# Required when STORAGE_STRATEGY is set to 'redis'
# This URL is used for both persistent data and temporary socket-related data
# Format: redis://[username:password@]host[:port][/database_number]
# Example: redis://user:<EMAIL>:6379/0
REDIS_URL=redis://localhost:6379

# Timeout in milliseconds for the whiteboard server's graceful shutdown process
# If the server cannot close all connections within this time, it will force shutdown
# Default: 3600000 (1 hour)
FORCE_CLOSE_TIMEOUT=3600000

# Maximum file upload size in megabytes
# This limits the size of files that can be uploaded to the whiteboard
# Default: 2 (MB)
MAX_UPLOAD_FILE_SIZE=2

# Prometheus metrics endpoint
# Set this to access the monitoring endpoint at /metrics
# either providing it as Bearer token or as ?token= query parameter
METRICS_TOKEN=

# WebSocket compression setting
# Enable or disable per-message deflate compression for WebSocket messages
# Default: true
COMPRESSION_ENABLED=true