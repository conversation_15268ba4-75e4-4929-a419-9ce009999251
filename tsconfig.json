{"extends": "@vue/tsconfig/tsconfig.json", "compilerOptions": {"allowSyntheticDefaultImports": true, "declaration": true, "esModuleInterop": true, "lib": ["DOM", "ESNext"], "noEmit": true, "outDir": "./js", "plugins": [{"name": "typescript-plugin-css-modules"}], "sourceMap": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "node", "strict": true, "jsx": "react-jsx", "skipLibCheck": true}, "exclude": ["js", "lib", "node_modules", "vendor"], "vueCompilerOptions": {"target": 2.7}}